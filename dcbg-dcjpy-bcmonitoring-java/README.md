# dcbg-dcjpy-bcmonitoring-java

## はじめに

dcbg-dcjpy-bcmonitoring-javaアプリケーションは、Ethereum互換のブロックチェーン（特にHyperledger Besu）上のスマートコントラクトからイベントを追跡・処理するために設計されたブロックチェーン監視サービスです。このSpring Bootアプリケーションは、WebSocketを介してブロックチェーンノードに接続し、特定のイベントを監視し、それらをDynamoDBに保存します。

## デプロイメントプロセス

### 1. 環境準備

以下のツールとサービスが必要です：

- Java 21（Amazon Corretto 21推奨）
- Gradle 8.x
- DockerとDocker Compose
- Ethereum互換のブロックチェーンノード（Hyperledger Besu）
- AWSサービスへのアクセス（開発環境ではLocalStackを使用可能）

### 2. ビルドプロセス

```bash
# リポジトリをクローン
git clone [リポジトリURL]
cd dcbg-dcjpy-bcmonitoring-java

# アプリケーションをビルド
./gradlew clean build
```

ビルドが成功すると、`build/libs/dcbg-dcjpy-bcmonitoring-0.0.1-SNAPSHOT.jar`にJARファイルが作成されます。

### 3. 環境設定

#### 開発環境

開発環境では、`docker-compose.yml`を使用してLocalStackでAWSサービスをシミュレートします：

```bash
# LocalStackを含む開発環境を起動
docker-compose up -d localstack

# LocalStackの初期化を確認
docker logs localstack
```

#### 本番環境

本番環境では、以下のAWSリソースが必要です：

- DynamoDBテーブル（Events、BlockHeight）
- S3バケット（ABIファイル用）

### 4. デプロイ手順

#### Dockerを使用したデプロイ

```bash
# イメージをビルド
docker build -t dcbg-dcjpy-bcmonitoring:latest .

# コンテナを起動
docker-compose up -d
```

#### 環境変数の設定

アプリケーションの動作は環境変数で制御されます。主な環境変数は以下の通りです：

```yaml
environment:
  # ブロックチェーンノード接続設定
  - WEBSOCKET_URI_HOST=host.docker.internal
  - WEBSOCKET_URI_PORT=18541

  # AWS/DynamoDB設定
  - DYNAMODB_ENDPOINT=http://localstack:4566
  - DYNAMODB_REGION=ap-northeast-1
  - DYNAMODB_TABLE_NAME_PREFIX=local

  # S3設定
  - S3_BUCKET_NAME=abijson-local-bucket
  - S3_REGION=ap-northeast-1

  # アプリケーション設定
  - SUBSCRIPTION_CHECK_INTERVAL=3000
  - EVENTS_TABLE_NAME=Events
  - BLOCK_HEIGHT_TABLE_NAME=BlockHeight
  - ENV=local
```

### 5. デプロイ後の検証

```bash
# アプリケーションログの確認
docker logs bcmonitoring

# DynamoDBテーブルの確認（開発環境）
awslocal --endpoint-url=http://localhost:4566 dynamodb scan --table-name local-Events
```

### 6. 監視とメンテナンス

- ログは構造化されており、以下の属性を含みます：
  - event_name（イベント名）
  - tx_hash（トランザクションハッシュ）
  - block_height（ブロック高）
  - log_index（ログインデックス）
  - block_timestamp（ブロックタイムスタンプ）

## アーキテクチャ概要

### デプロイメントフロー

1. アプリケーションのビルドとDockerイメージの作成
2. 必要なAWSリソース（DynamoDB、S3）のプロビジョニング
3. 環境変数の設定とコンテナの起動
4. アプリケーションの初期化（ABI定義のダウンロード）
5. ブロックチェーンノードへの接続とイベント監視の開始
6. イベントの処理とDynamoDBへの保存